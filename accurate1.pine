// This Pine Script™ code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © fluxchart

//@version=6
//S&R V2.12
const bool DEBUG = false
const bool fixSRs = true
const bool fixRetests = false

indicator("buy or sell [3] by vickymosafan", overlay = true, max_labels_count = 500, max_lines_count = 500, max_boxes_count = 500, dynamic_requests = true)

const int maxSRInfoListSize = 10
const int maxBarInfoListSize = 3000
const int maxDistanceToLastBar = 500
const int minSRSize = 5
const int retestLabelCooldown = 3
const float tooCloseATR = 1.0 / 8.0
const int labelOffsetBars = 20

const int atrLen = 20
atr = ta.atr(atrLen)
avgVolume = ta.sma(volume, atrLen)

var int curTFMS = timeframe.in_seconds(timeframe.period) * 1000
var map<string, bool> alerts = map.new<string, bool>()
alerts.put("Retest", false)
alerts.put("Break", false)

srPivotLength = input.int(15, "Pivot Length", minval = 3, maxval = 50, group = "General Configuration", display = display.none)
srStrength = input.int(1, "Strength", [1, 2, 3], group = "General Configuration", display = display.none)
srInvalidation = input.string("Close", "Invalidation", ["Wick", "Close"], group = "General Configuration", display = display.none)
expandZones = input.string("Only Valid", "Expand Lines & Zones", options = ["All", "Only Valid", "None"], group = "General Configuration", display = display.none)
showInvalidated = input.bool(true, "Show Invalidated", group = "General Configuration", display = display.none)

timeframe1Enabled = input.bool(true, title = "", group = "Timeframes", inline = "timeframe1", display = display.none)
timeframe1 = input.timeframe("", title = "", group = "Timeframes", inline = "timeframe1", display = display.none)
timeframe2Enabled = input.bool(false, title = "", group = "Timeframes", inline = "timeframe2", display = display.none)
timeframe2 = input.timeframe("D", title = "", group = "Timeframes", inline = "timeframe2", display = display.none)
timeframe3Enabled = input.bool(false, title = "", group = "Timeframes", inline = "timeframe3", display = display.none)
timeframe3 = input.timeframe("W", title = "", group = "Timeframes", inline = "timeframe3", display = display.none)

showBreaks = input.bool(false, "Show Breaks", group = "Breaks & Retests", inline = "ShowBR", display = display.none)
showRetests = input.bool(false, "Show Retests", group = "Breaks & Retests", inline = "ShowBR", display = display.none)
avoidFalseBreaks = input.bool(false, "Avoid False Breaks", group = "Breaks & Retests", display = display.none)
breakVolumeThreshold = input.float(0.3, "Break Volume Threshold", minval = 0.1, maxval = 2.0, step = 0.1, group = "Breaks & Retests", tooltip = "Only taken into account if Avoid False Breakouts is enabled.\nHigher values mean it's less likely to be a break.", display = display.none)
inverseBrokenLineColor = input.bool(false, "Inverse Color After Broken", group = "Breaks & Retests", display = display.none)

styleMode = input.string("Lines", "Style", ["Lines", "Zones"], group = "Style", display = display.none)
lineStyle = input.string("____", "Line Style", ["____", "----", "...."], group = "Style", display = display.none)
lineWidth = input.int(2, "Line Width", minval = 1, group = "Style", display = display.none)
zoneSize = input.float(1.0, "Zone Width", minval = 0.1, maxval = 10, step = 0.1, group = "Style", display = display.none)
zoneSizeATR = zoneSize * 0.075
supportColor = input.color(#********, "Support Color", group = "Style", inline = "RScolors", display = display.none)
resistanceColor = input.color(#f2364580, "Resistance Color", group = "Style", inline = "RScolors", display = display.none)
breakColor = input.color(color.blue, "Break Color", group = "Style", inline = "RScolors2", display = display.none)
textColor = input.color(#ffffff80, "Text Color", group = "Style", inline = "RScolors2", display = display.none)

enableRetestAlerts = input.bool(true, "Enable Retest Alerts", tooltip = "Needs Show Retests option enabled.", group = "Alerts", display = display.none)
enableBreakAlerts = input.bool(true, "Enable Break Alerts", tooltip = "Needs Show Breaks option enabled.", group = "Alerts", display = display.none)

insideBounds = (bar_index > last_bar_index - maxDistanceToLastBar)

type srInfo
    int startTime
    float price
    string srType
    int strength
    string timeframeStr
    bool ephemeral = false

    int breakTime
    array<int> retestTimes

type srObj
    srInfo info
    
    bool startFixed
    bool breakFixed

    bool rendered
    string combinedTimeframeStr

    line srLine
    box srBox
    label srLabel
    label breakLabel
    array<label> retestLabels

type barInfo
    int t
    int tc
    float c
    float h
    float l

var allSRList = array.new<srObj>()

//#region Find Val RTN Time
findValRtnTime (barInfo[] biList, valToFind, toSearch, searchMode, minTime, maxTime, int defVal = na) =>
    int rtnTime = defVal
    float minDiff = na
    if biList.size() > 0
        for i = biList.size() - 1 to 0
            curBI = biList.get(i)
            if curBI.t >= minTime and curBI.t < maxTime
                toLook = (toSearch == "Low" ? curBI.l : toSearch == "High" ? curBI.h : curBI.c)
                if searchMode == "Nearest"
                    curDiff = math.abs(valToFind - toLook)
                    if na(minDiff)
                        rtnTime := curBI.t
                        minDiff := curDiff
                    else
                        if curDiff <= minDiff
                            minDiff := curDiff
                            rtnTime := curBI.t
                if searchMode == "Higher"
                    if toLook >= valToFind
                        rtnTime := curBI.t
                        break
                if searchMode == "Lower"
                    if toLook <= valToFind
                        rtnTime := curBI.t
                        break
    rtnTime
//#endregion

formatTimeframeString (string formatTimeframe, bool short = false) =>
    timeframeF = (formatTimeframe == "" ? timeframe.period : formatTimeframe)
    if str.contains(timeframeF, "D") or str.contains(timeframeF, "W") or str.contains(timeframeF, "S") or str.contains(timeframeF, "M")
        timeframe.from_seconds(timeframe.in_seconds(timeframeF))
    else
        seconds = timeframe.in_seconds(timeframeF)
        if seconds >= 3600
            hourCount = int(seconds / 3600)
            if short
                str.tostring(hourCount) + "h"
            else
                str.tostring(hourCount) + " Hour" + (hourCount > 1 ? "s" : "")
        else
            if short
                timeframeF + "m"
            else
                timeframeF + " Min"

renderSRObj (srObj sr) =>
    if na(sr.info.breakTime) or showInvalidated
        sr.rendered := true
        endTime = nz(sr.info.breakTime, time + curTFMS * labelOffsetBars)
        extendType = extend.none
        if na(sr.info.breakTime)
            extendType := extend.right
        if expandZones == "Only Valid" and na(sr.info.breakTime)
            extendType := extend.both
        else if expandZones == "All"
            extendType := extend.both
            endTime := time + curTFMS * labelOffsetBars
        
        labelTitle = formatTimeframeString(sr.info.timeframeStr)
        if not na(sr.combinedTimeframeStr)
            labelTitle := sr.combinedTimeframeStr

        labelTitle += " | " + str.tostring(sr.info.price, format.mintick) + ((sr.info.ephemeral and DEBUG) ? " [E]" : "")
        if styleMode == "Lines"
            // Line with Dynamic Color
            line_color = sr.info.srType == "Resistance" ? dynamic_resistance_color : dynamic_support_color
            sr.srLine := line.new(sr.info.startTime, sr.info.price, endTime, sr.info.price, xloc = xloc.bar_time, color = line_color, width = lineWidth, style = lineStyle == "----" ? line.style_dashed : lineStyle == "...." ? line.style_dotted : line.style_solid, extend = extendType)
            // Label
            sr.srLabel := label.new(extendType == extend.none ? ((sr.info.startTime + endTime) / 2) : endTime, sr.info.price, xloc = xloc.bar_time, text = labelTitle, textcolor = textColor, style = label.style_none)
        else
            // Zone with Dynamic Color
            zone_color = sr.info.srType == "Resistance" ? dynamic_resistance_color : dynamic_support_color
            sr.srBox := box.new(sr.info.startTime, sr.info.price + atr * zoneSizeATR, endTime, sr.info.price - atr * zoneSizeATR, xloc = xloc.bar_time, bgcolor = zone_color, border_color = na, text = labelTitle, text_color = textColor, extend = extendType, text_size = size.normal, text_halign = (extendType != extend.none) ? text.align_right : text.align_center)

        // Break Label
        if showBreaks
            if not na(sr.info.breakTime)
                sr.breakLabel := label.new(sr.info.breakTime, sr.info.price, "B", yloc = sr.info.srType ==  "Resistance" ? yloc.belowbar : yloc.abovebar, style = sr.info.srType == "Resistance" ? label.style_label_up : label.style_label_down, color = breakColor, textcolor = color.new(textColor, 0), xloc = xloc.bar_time, size = size.small)
                if (time - curTFMS <= sr.info.breakTime) and (time + curTFMS >= sr.info.breakTime)
                    alerts.put("Break", true)

        // Retest Labels
        if showRetests
            if sr.info.retestTimes.size() > 0
                for i = sr.info.retestTimes.size() - 1 to 0
                    curRetestTime = sr.info.retestTimes.get(i)
                    cooldownOK = true
                    if sr.retestLabels.size() > 0
                        lastLabel = sr.retestLabels.get(0)
                        if math.abs(lastLabel.get_x() - curRetestTime) < curTFMS * retestLabelCooldown
                            cooldownOK := false

                    if cooldownOK and (curRetestTime >= sr.info.startTime) and (na(sr.info.breakTime) or curRetestTime < sr.info.breakTime)
                        if time - curTFMS <= curRetestTime and time >= curRetestTime
                            alerts.put("Retest", true)
                        retest_color = sr.info.srType == "Resistance" ? dynamic_resistance_color : dynamic_support_color
                        sr.retestLabels.unshift(label.new(curRetestTime, sr.info.price, "R" + (DEBUG ?  (" " + str.tostring(sr.info.price)) : ""), yloc = sr.info.srType ==  "Resistance" ? yloc.abovebar : yloc.belowbar, style = sr.info.srType == "Resistance" ? label.style_label_down : label.style_label_up, color = retest_color, textcolor = color.new(textColor, 0), xloc = xloc.bar_time, size = size.small))

safeDeleteSRObj (srObj sr) =>
    if sr.rendered
        line.delete(sr.srLine)
        box.delete(sr.srBox)
        label.delete(sr.srLabel)
        label.delete(sr.breakLabel)
        if sr.retestLabels.size() > 0
            for i = 0 to sr.retestLabels.size() - 1
                curRetestLabel = sr.retestLabels.get(i)
                label.delete(curRetestLabel)
        sr.rendered := false

var allSRInfoList = array.new<srInfo>()
var barInfoList = array.new<barInfo>()

pivotHigh = ta.pivothigh(srPivotLength, srPivotLength)
pivotLow = ta.pivotlow(srPivotLength, srPivotLength)

barInfoList.unshift(barInfo.new(time, time_close, close, high, low))
if barInfoList.size() > maxBarInfoListSize
    barInfoList.pop()

if insideBounds and barstate.isconfirmed
    // Find Supports
    if not na(pivotLow)
        validSR = true
        if allSRInfoList.size() > 0
            for i = 0 to allSRInfoList.size() - 1
                curRSInfo = allSRInfoList.get(i)
                if (math.abs(curRSInfo.price - pivotLow) < atr * tooCloseATR) and na(curRSInfo.breakTime)
                    validSR := false
                    break
        
        if validSR
            newSRInfo = srInfo.new(barInfoList.get(srPivotLength).t, pivotLow, "Support", 1, timeframe.period)
            newSRInfo.retestTimes := array.new<int>()

            //for i = 1 to srPivotLength
                //curBI = barInfoList.get(i)
                //if (curBI.l <= newSRInfo.price and curBI.c >= newSRInfo.price)
                    //newSRInfo.strength += 1
                    //if curBI.t != newSRInfo.startTime
                        //newSRInfo.retestTimes.unshift(curBI.t)
            
            allSRInfoList.unshift(newSRInfo)
            while allSRInfoList.size() > maxSRInfoListSize
                allSRInfoList.pop()
    
    // Find Resistances
    if not na(pivotHigh)
        validSR = true
        if allSRInfoList.size() > 0
            for i = 0 to allSRInfoList.size() - 1
                curRSInfo = allSRInfoList.get(i)
                if (math.abs(curRSInfo.price - pivotLow) < atr * tooCloseATR) and na(curRSInfo.breakTime)
                    validSR := false
                    break
        if validSR
            newSRInfo = srInfo.new(barInfoList.get(srPivotLength).t, pivotHigh, "Resistance", 1, timeframe.period)
            newSRInfo.retestTimes := array.new<int>()

            //for i = 1 to srPivotLength
                //curBI = barInfoList.get(i)
                //if (curBI.h >= newSRInfo.price and curBI.c <= newSRInfo.price)
                    //newSRInfo.strength += 1
                    //if curBI.t != newSRInfo.startTime
                        //newSRInfo.retestTimes.unshift(curBI.t)

            allSRInfoList.unshift(newSRInfo)
            if allSRInfoList.size() > maxSRInfoListSize
                allSRInfoList.pop()

// Handle SR Infos
if insideBounds and (srInvalidation == "Wick" or barstate.isconfirmed)
    if allSRInfoList.size() > 0
        for i = 0 to allSRInfoList.size() - 1
            srInfo curSRInfo = allSRInfoList.get(i)
            
            // Breaks
            invHigh = (srInvalidation == "Close" ? close : high)
            invLow = (srInvalidation == "Close" ? close : low)
            closeTime = time
            if na(curSRInfo.breakTime)
                if curSRInfo.srType == "Resistance" and invHigh > curSRInfo.price
                    if (not avoidFalseBreaks) or (volume > avgVolume * breakVolumeThreshold)
                        curSRInfo.breakTime := closeTime
                        if inverseBrokenLineColor and (not curSRInfo.ephemeral) and curSRInfo.strength >= srStrength
                            ephSR = srInfo.new(closeTime, curSRInfo.price, "Support", curSRInfo.strength, curSRInfo.timeframeStr, true)
                            ephSR.retestTimes := array.new<int>()
                            allSRInfoList.unshift(ephSR)
                else if curSRInfo.srType == "Support" and invLow < curSRInfo.price
                    if (not avoidFalseBreaks) or (volume > avgVolume * breakVolumeThreshold)
                        curSRInfo.breakTime := closeTime
                        if inverseBrokenLineColor and (not curSRInfo.ephemeral) and curSRInfo.strength >= srStrength
                            ephSR = srInfo.new(closeTime, curSRInfo.price, "Resistance", curSRInfo.strength, curSRInfo.timeframeStr, true)
                            ephSR.retestTimes := array.new<int>()
                            allSRInfoList.unshift(ephSR)
                
            // Strength & Retests
            if na(curSRInfo.breakTime) and time > curSRInfo.startTime and barstate.isconfirmed
                if curSRInfo.srType == "Resistance" and high >= curSRInfo.price and close <= curSRInfo.price
                    int lastRetestTime = 0
                    if curSRInfo.retestTimes.size() > 0
                        lastRetestTime := curSRInfo.retestTimes.get(0)
                    
                    if lastRetestTime != time
                        if not curSRInfo.ephemeral
                            curSRInfo.strength += 1
                        curSRInfo.retestTimes.unshift(time)
                
                else if curSRInfo.srType == "Support" and low <= curSRInfo.price and close >= curSRInfo.price
                    int lastRetestTime = 0
                    if curSRInfo.retestTimes.size() > 0
                        lastRetestTime := curSRInfo.retestTimes.get(0)
                    
                    if lastRetestTime != time
                        if not curSRInfo.ephemeral
                            curSRInfo.strength += 1
                        curSRInfo.retestTimes.unshift(time)

fixSRToTimeframe (srObj sr) =>
    srMS = math.max(timeframe.in_seconds(sr.info.timeframeStr), timeframe.in_seconds()) * 1000
    if (not sr.startFixed)
        if not sr.info.ephemeral
            if sr.info.srType == "Resistance"
                sr.info.startTime := findValRtnTime(barInfoList, sr.info.price, "High", "Nearest", sr.info.startTime - srMS, sr.info.startTime + srMS, sr.info.startTime)
            else
                sr.info.startTime := findValRtnTime(barInfoList, sr.info.price, "Low", "Nearest", sr.info.startTime - srMS, sr.info.startTime + srMS, sr.info.startTime)
            sr.startFixed := true
        else
            if allSRList.size() > 0
                for i = 0 to allSRList.size() - 1
                    curSR = allSRList.get(i)
                    if (not curSR.info.ephemeral) and (not na(curSR.info.breakTime)) and curSR.info.price == sr.info.price and ((sr.info.srType == "Resistance" and curSR.info.srType == "Support") or (sr.info.srType == "Support" and curSR.info.srType == "Resistance"))
                        if curSR.breakFixed
                            sr.info.startTime := curSR.info.breakTime
                            sr.startFixed := true
                        break

    if not na(sr.info.breakTime)
        if (not sr.breakFixed)
            if sr.info.srType == "Resistance"
                sr.info.breakTime := findValRtnTime(barInfoList, sr.info.price, srInvalidation == "Wick" ? "High" : "Close", "Higher", sr.info.breakTime - srMS, sr.info.breakTime + srMS, sr.info.breakTime)
            else
                sr.info.breakTime := findValRtnTime(barInfoList, sr.info.price, srInvalidation == "Wick" ? "Low" : "Close", "Lower", sr.info.breakTime - srMS, sr.info.breakTime + srMS, sr.info.breakTime)
            sr.breakFixed := true
    
    if sr.info.retestTimes.size() > 0 and fixRetests
        for i = 0 to sr.info.retestTimes.size() - 1
            curRetestTime = sr.info.retestTimes.get(i)

            retestStartTime = curRetestTime - srMS
            retestStartTime := math.max(retestStartTime, sr.info.startTime + 1)
            
            retestEndTime = curRetestTime + srMS
            if not na(sr.info.breakTime)
                retestEndTime := math.min(retestEndTime, sr.info.breakTime - 1)
            
            if sr.info.srType == "Resistance"
                sr.info.retestTimes.set(i, findValRtnTime(barInfoList, sr.info.price, "High", "Higher", retestStartTime, retestEndTime, sr.info.retestTimes.get(i)))
            else
                sr.info.retestTimes.set(i, findValRtnTime(barInfoList, sr.info.price, "Low", "Lower", retestStartTime, retestEndTime, sr.info.retestTimes.get(i)))

getSR (srObj[] list, srPrice, eph, srType, timeframeStr) =>
    srObj rtnSR = na
    if list.size() > 0
        for i = 0 to list.size() - 1
            curSR = list.get(i)
            if curSR.info.price == srPrice and curSR.info.ephemeral == eph and curSR.info.srType == srType and curSR.info.timeframeStr == timeframeStr
                rtnSR := curSR
                break
    rtnSR

// Handle SR
handleTF (tfStr, tfEnabled) =>
    if tfEnabled
        tfSRInfoList = request.security(syminfo.tickerid, tfStr, allSRInfoList)
        if not na(tfSRInfoList) and tfSRInfoList.size() > 0
            for i = 0 to tfSRInfoList.size() - 1
                srInfo curSRInfo = tfSRInfoList.get(i)
                if fixSRs
                    currentSameSR = getSR(allSRList, curSRInfo.price, curSRInfo.ephemeral, curSRInfo.srType, curSRInfo.timeframeStr)
                    if not na(currentSameSR)
                        if currentSameSR.startFixed
                            curSRInfo.startTime := currentSameSR.info.startTime
                        if currentSameSR.breakFixed
                            curSRInfo.breakTime := currentSameSR.info.breakTime
                        curSRInfo.retestTimes := currentSameSR.info.retestTimes
                        // All other info should be replaced except fixed start, break and all retests.
                        currentSameSR.info := curSRInfo
                        if not currentSameSR.breakFixed
                            fixSRToTimeframe(currentSameSR)
                    else
                        srObj newSRObj = srObj.new(curSRInfo)
                        // We handle retests in current timeframe so no need to get them from upper.
                        newSRObj.info.retestTimes := array.new<int>()
                        newSRObj.retestLabels := array.new<label>()
                        fixSRToTimeframe(newSRObj)
                        allSRList.unshift(newSRObj)
                else
                    srObj newSRObj = srObj.new(curSRInfo)
                    newSRObj.retestLabels := array.new<label>()
                    allSRList.unshift(newSRObj)
    true

if (bar_index > last_bar_index - maxDistanceToLastBar * 8) and barstate.isconfirmed
    if not fixSRs
        if allSRList.size() > 0
            for i = 0 to allSRList.size() - 1
                srObj curSRObj = allSRList.get(i)
                safeDeleteSRObj(curSRObj)
        allSRList.clear()
        
    handleTF(timeframe1, timeframe1Enabled)
    handleTF(timeframe2, timeframe2Enabled)
    handleTF(timeframe3, timeframe3Enabled)
    
    if allSRList.size() > 0
        for i = 0 to allSRList.size() - 1
            srObj curSRObj = allSRList.get(i)
            safeDeleteSRObj(curSRObj)
            tooClose = false
            for j = 0 to allSRList.size() - 1
                closeSR = allSRList.get(j)
                if closeSR.rendered and math.abs(closeSR.info.price - curSRObj.info.price) <= tooCloseATR * atr and closeSR.info.srType == curSRObj.info.srType and closeSR.info.ephemeral == curSRObj.info.ephemeral
                    tooClose := true
                    if not str.contains((na(closeSR.combinedTimeframeStr) ? formatTimeframeString(closeSR.info.timeframeStr) : closeSR.combinedTimeframeStr), formatTimeframeString(curSRObj.info.timeframeStr))
                        if na(closeSR.combinedTimeframeStr)
                            closeSR.combinedTimeframeStr := formatTimeframeString(closeSR.info.timeframeStr) + " & " + formatTimeframeString(curSRObj.info.timeframeStr)
                        else
                            closeSR.combinedTimeframeStr += " & " + formatTimeframeString(curSRObj.info.timeframeStr)
                    break
            
            if (curSRObj.info.strength >= srStrength) and (na(curSRObj.info.breakTime) or (curSRObj.info.breakTime - curSRObj.info.startTime) >= minSRSize * curTFMS) and (not tooClose)
                renderSRObj(curSRObj)

// Current Timeframe Retests
if allSRList.size() > 0 and barstate.isconfirmed
    for i = 0 to allSRList.size() - 1
        srObj curSR = allSRList.get(i)
        if na(curSR.info.breakTime) and time > curSR.info.startTime
            if curSR.info.srType == "Resistance" and high >= curSR.info.price and close <= curSR.info.price
                int lastRetestTime = 0
                if curSR.info.retestTimes.size() > 0
                    lastRetestTime := curSR.info.retestTimes.get(0)
                
                if lastRetestTime != time
                    curSR.info.retestTimes.unshift(time)
            
            else if curSR.info.srType == "Support" and low <= curSR.info.price and close >= curSR.info.price
                int lastRetestTime = 0
                if curSR.info.retestTimes.size() > 0
                    lastRetestTime := curSR.info.retestTimes.get(0)
                
                if lastRetestTime != time
                    curSR.info.retestTimes.unshift(time)

//plotchar(alerts.get("Break") ? high : na, "", "✅", size = size.normal)
//plotchar(alerts.get("Retest") ? high : na, "", "❤️", size = size.normal, location = location.belowbar)

alertcondition(alerts.get("Retest"), "New Retest", "")
alertcondition(alerts.get("Break"), "New Break", "")

if enableRetestAlerts and alerts.get("Retest")
    alert("New Retests Occured.")

if enableBreakAlerts and alerts.get("Break")
    alert("New Breaks Occured.")

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// 📊 INPUT PARAMETERS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// EMA Settings for Trend Analysis
ema_fast = input.int(9, title="Fast EMA", minval=1, group="📈 Trend Analysis")
ema_medium = input.int(21, title="Medium EMA", minval=1, group="📈 Trend Analysis")
ema_slow = input.int(50, title="Slow EMA", minval=1, group="📈 Trend Analysis")

// RSI Settings for Momentum
rsi_length = input.int(14, title="RSI Length", minval=1, group="📊 Momentum")
rsi_overbought = input.int(70, title="RSI Overbought", minval=50, maxval=100, group="📊 Momentum")
rsi_oversold = input.int(30, title="RSI Oversold", minval=0, maxval=50, group="📊 Momentum")

// MACD Settings for Confirmation
macd_fast = input.int(12, title="MACD Fast", minval=1, group="🔄 MACD")
macd_slow = input.int(26, title="MACD Slow", minval=1, group="🔄 MACD")
macd_signal = input.int(9, title="MACD Signal", minval=1, group="🔄 MACD")

// Signal Filtering
use_trend_filter = input.bool(true, title="Use Trend Filter", group="🎯 Signal Filtering")
min_signal_gap = input.int(10, title="Minimum Bars Between Signals", minval=1, group="🎯 Signal Filtering")

// Visual Settings
show_emas = input.bool(false, title="Show EMAs", group="🎨 Visual")
show_signals = input.bool(true, title="Show Buy/Sell Signals", group="🎨 Visual")
show_background = input.bool(false, title="Show Trend Background", group="🎨 Visual")

// Color Trend Settings
enable_color_trend = input.bool(true, title="Enable Color Trend Integration", group="🌈 Color Trend")
color_trend_method = input.string("EMA Cross", title="Color Method", options=["EMA Cross", "Price vs EMA", "Multi-EMA", "MACD"], group="🌈 Color Trend")
color_intensity = input.int(70, title="Color Intensity", minval=0, maxval=100, group="🌈 Color Trend")
show_trend_candles = input.bool(true, title="Color Candles", group="🌈 Color Trend")
show_trend_sr = input.bool(true, title="Color S&R Lines", group="🌈 Color Trend")
show_trend_signals = input.bool(true, title="Enhanced Signal Colors", group="🌈 Color Trend")

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// 📈 TECHNICAL INDICATORS CALCULATION
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// EMAs for Trend Analysis
ema_9 = ta.ema(close, ema_fast)
ema_21 = ta.ema(close, ema_medium)
ema_50 = ta.ema(close, ema_slow)

// RSI for Momentum
rsi = ta.rsi(close, rsi_length)

// MACD for Confirmation
[macd_line, signal_line, _] = ta.macd(close, macd_fast, macd_slow, macd_signal)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// 🎯 TREND ANALYSIS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// Multi-EMA Trend Detection
bullish_trend = ema_9 > ema_21 and ema_21 > ema_50 and close > ema_9
bearish_trend = ema_9 < ema_21 and ema_21 < ema_50 and close < ema_9
neutral_trend = not bullish_trend and not bearish_trend

// Trend Strength
trend_strength = math.abs(ema_9 - ema_50) / ema_50 * 100

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// 🌈 COLOR TREND ANALYSIS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// Color Trend Determination
color_trend_bullish = switch color_trend_method
    "EMA Cross" => ema_9 > ema_21
    "Price vs EMA" => close > ema_21
    "Multi-EMA" => bullish_trend
    "MACD" => macd_line > signal_line
    => bullish_trend

color_trend_bearish = switch color_trend_method
    "EMA Cross" => ema_9 < ema_21
    "Price vs EMA" => close < ema_21
    "Multi-EMA" => bearish_trend
    "MACD" => macd_line < signal_line
    => bearish_trend

// Dynamic Color Calculation
trend_color_bull = color.new(color.green, 100 - color_intensity)
trend_color_bear = color.new(color.red, 100 - color_intensity)
trend_color_neutral = color.new(color.gray, 100 - color_intensity)

// Current Trend Color
current_trend_color = color_trend_bullish ? trend_color_bull : color_trend_bearish ? trend_color_bear : trend_color_neutral

// Enhanced S&R Colors with Trend Integration
dynamic_support_color = show_trend_sr and enable_color_trend ?
     (color_trend_bullish ? color.new(color.green, 50) : color.new(#********, 0)) : supportColor

dynamic_resistance_color = show_trend_sr and enable_color_trend ?
     (color_trend_bearish ? color.new(color.red, 50) : color.new(#f2364580, 0)) : resistanceColor





// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// 🔍 SIGNAL GENERATION
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// Basic Conditions
ema_bullish_cross = ta.crossover(ema_9, ema_21)
ema_bearish_cross = ta.crossunder(ema_9, ema_21)

// RSI Conditions
rsi_bullish = rsi < rsi_overbought and rsi > 40  // Not overbought, has room to rise
rsi_bearish = rsi > rsi_oversold and rsi < 60   // Not oversold, has room to fall

// MACD Conditions
macd_bullish = macd_line > signal_line and macd_line > macd_line[1]
macd_bearish = macd_line < signal_line and macd_line < macd_line[1]

// Price Action Confirmation
price_above_ema21 = close > ema_21
price_below_ema21 = close < ema_21

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// 🎯 FILTERED SIGNAL CONDITIONS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// BUY Signal Conditions
buy_condition = ema_bullish_cross and rsi_bullish and macd_bullish and price_above_ema21 and (not use_trend_filter or bullish_trend or neutral_trend)

// SELL Signal Conditions
sell_condition = ema_bearish_cross and rsi_bearish and macd_bearish and price_below_ema21 and (not use_trend_filter or bearish_trend or neutral_trend)

// Signal Gap Filter
var int last_buy_bar = na
var int last_sell_bar = na

buy_signal = buy_condition and (na(last_buy_bar) or bar_index - last_buy_bar >= min_signal_gap)
sell_signal = sell_condition and (na(last_sell_bar) or bar_index - last_sell_bar >= min_signal_gap)

// Update last signal bars
if buy_signal
    last_buy_bar := bar_index
if sell_signal
    last_sell_bar := bar_index

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// 🎨 VISUAL ELEMENTS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// EMA Plots with Dynamic Colors
ema9_color = enable_color_trend ? (color_trend_bullish ? color.new(color.lime, 0) : color_trend_bearish ? color.new(color.red, 0) : color.new(color.blue, 0)) : color.new(color.blue, 0)
ema21_color = enable_color_trend ? (color_trend_bullish ? color.new(color.green, 0) : color_trend_bearish ? color.new(color.maroon, 0) : color.new(color.orange, 0)) : color.new(color.orange, 0)
ema50_color = enable_color_trend ? (color_trend_bullish ? color.new(color.olive, 0) : color_trend_bearish ? color.new(color.red, 0) : color.new(color.red, 0)) : color.new(color.red, 0)

plot(show_emas ? ema_9 : na, title="EMA 9", color=ema9_color, linewidth=2)
plot(show_emas ? ema_21 : na, title="EMA 21", color=ema21_color, linewidth=2)
plot(show_emas ? ema_50 : na, title="EMA 50", color=ema50_color, linewidth=2)

// Enhanced Trend Background
bg_color = show_background ?
     (enable_color_trend ? current_trend_color :
      (bullish_trend ? color.new(color.green, 95) : bearish_trend ? color.new(color.red, 95) : color.new(color.gray, 98))) : na
bgcolor(bg_color, title="Trend Background")

// Candle Coloring
candle_color = show_trend_candles and enable_color_trend ? current_trend_color : na
barcolor(candle_color, title="Trend Candles")

// Enhanced Buy/Sell Signals with Dynamic Colors
buy_signal_color = show_trend_signals and enable_color_trend ?
     (color_trend_bullish ? color.new(color.lime, 0) : color.new(color.green, 0)) : color.new(color.green, 0)

sell_signal_color = show_trend_signals and enable_color_trend ?
     (color_trend_bearish ? color.new(color.red, 0) : color.new(color.red, 0)) : color.new(color.red, 0)

plotshape(show_signals and buy_signal, title="BUY Signal", location=location.belowbar, style=shape.labelup, size=size.tiny, color=buy_signal_color, textcolor=color.white, text="BUY")

plotshape(show_signals and sell_signal, title="SELL Signal", location=location.abovebar, style=shape.labeldown, size=size.tiny, color=sell_signal_color, textcolor=color.white, text="SELL")

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// 📊 DASHBOARD (Table Display)
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// Create Enhanced Dashboard Table
var table dashboard = table.new(position.top_right, 2, 7, bgcolor=color.new(color.white, 80), border_width=1)

if barstate.islast
    // Header
    table.cell(dashboard, 0, 0, "Indicator", text_color=color.black, text_size=size.small, bgcolor=color.new(color.gray, 70))
    table.cell(dashboard, 1, 0, "Status", text_color=color.black, text_size=size.small, bgcolor=color.new(color.gray, 70))

    // Trend Status
    trend_text = bullish_trend ? "BULLISH" : bearish_trend ? "BEARISH" : "NEUTRAL"
    trend_color = bullish_trend ? color.green : bearish_trend ? color.red : color.gray
    table.cell(dashboard, 0, 1, "Trend", text_color=color.black, text_size=size.small)
    table.cell(dashboard, 1, 1, trend_text, text_color=color.white, text_size=size.small, bgcolor=color.new(trend_color, 20))

    // Color Trend Status
    color_trend_text = color_trend_bullish ? "BULLISH" : color_trend_bearish ? "BEARISH" : "NEUTRAL"
    color_trend_color = color_trend_bullish ? color.green : color_trend_bearish ? color.red : color.gray
    table.cell(dashboard, 0, 2, "Color Trend", text_color=color.black, text_size=size.small)
    table.cell(dashboard, 1, 2, color_trend_text, text_color=color.white, text_size=size.small, bgcolor=color.new(color_trend_color, 20))

    // RSI Status
    rsi_text = rsi > rsi_overbought ? "OVERBOUGHT" : rsi < rsi_oversold ? "OVERSOLD" : "NEUTRAL"
    rsi_color = rsi > rsi_overbought ? color.red : rsi < rsi_oversold ? color.green : color.gray
    table.cell(dashboard, 0, 3, "RSI (" + str.tostring(math.round(rsi, 1)) + ")", text_color=color.black, text_size=size.small)
    table.cell(dashboard, 1, 3, rsi_text, text_color=color.white, text_size=size.small, bgcolor=color.new(rsi_color, 20))

    // MACD Status
    macd_text = macd_line > signal_line ? "BULLISH" : "BEARISH"
    macd_color = macd_line > signal_line ? color.green : color.red
    table.cell(dashboard, 0, 4, "MACD", text_color=color.black, text_size=size.small)
    table.cell(dashboard, 1, 4, macd_text, text_color=color.white, text_size=size.small, bgcolor=color.new(macd_color, 20))

    // Current Signal
    current_signal = buy_signal ? "BUY" : sell_signal ? "SELL" : "WAIT"
    signal_color = buy_signal ? color.green : sell_signal ? color.red : color.gray
    table.cell(dashboard, 0, 5, "Signal", text_color=color.black, text_size=size.small)
    table.cell(dashboard, 1, 5, current_signal, text_color=color.white, text_size=size.small, bgcolor=color.new(signal_color, 20))

    // Trend Strength
    strength_text = trend_strength > 2 ? "STRONG" : trend_strength > 1 ? "MODERATE" : "WEAK"
    table.cell(dashboard, 0, 6, "Strength", text_color=color.black, text_size=size.small)
    table.cell(dashboard, 1, 6, strength_text, text_color=color.black, text_size=size.small)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// 🚨 ALERTS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// Alert Conditions
alertcondition(buy_signal, title="BUY Signal Alert", message="🟢 BUY Signal Generated! Trend: {{plot_0}}, RSI: {{plot_1}}")
alertcondition(sell_signal, title="SELL Signal Alert", message="🔴 SELL Signal Generated! Trend: {{plot_0}}, RSI: {{plot_1}}")
alertcondition(buy_signal or sell_signal, title="Any Signal Alert", message="⚡ Trading Signal: {{plot_0}}")

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// 📝 NOTES & STRATEGY EXPLANATION
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// This indicator combines multiple technical analysis methods for accurate signal generation:
//
// 1. TREND ANALYSIS: Uses 3 EMAs (9, 21, 50) to determine market trend direction
//    - Bullish: EMA9 > EMA21 > EMA50 and price > EMA9
//    - Bearish: EMA9 < EMA21 < EMA50 and price < EMA9
//
// 2. MOMENTUM: RSI (14) to avoid overbought/oversold extremes
//    - Buy signals avoid RSI > 70 (overbought)
//    - Sell signals avoid RSI < 30 (oversold)
//
// 3. CONFIRMATION: MACD (12,26,9) for additional momentum confirmation
//    - Buy: MACD line > Signal line and rising
//    - Sell: MACD line < Signal line and falling
//
// 4. FILTERING: Multiple filters to reduce false signals
//    - Trend filter: Only trade in direction of main trend
//    - Signal gap: Minimum bars between signals to avoid noise
//    - Price confirmation: Price must be on correct side of EMA21
//
// 5. ACCURACY FOCUS: Designed to show SELL signals in bearish markets (like current XAUUSD)
//    and BUY signals in bullish markets, ensuring alignment with market direction
//
// 6. COLOR TREND INTEGRATION: Dynamic color system that adapts to market conditions
//    - Support/Resistance lines change color based on trend direction
//    - Candles can be colored according to trend (optional)
//    - EMAs use dynamic colors to show trend strength
//    - Enhanced signal colors for better visual clarity
//    - Multiple color methods: EMA Cross, Price vs EMA, Multi-EMA, MACD
//    - Adjustable color intensity for personal preference
//    - Integrated dashboard shows both main trend and color trend status